import { SessionMemory, ChatMessage } from '../types';
export declare class MemoryService {
    private sessions;
    private maxMessages;
    private sessionTimeoutHours;
    constructor();
    getSessionMemory(sessionId: string): SessionMemory;
    addMessage(sessionId: string, role: 'user' | 'assistant', content: string): void;
    getRecentMessages(sessionId: string, count?: number): ChatMessage[];
    getConversationSummary(sessionId: string): string;
    clearSession(sessionId: string): void;
    getActiveSessions(): string[];
    getSessionStats(sessionId: string): {
        message_count: number;
        created_at: string;
        last_updated: string;
        session_age_hours: number;
    };
    private cleanupExpiredSessions;
    private getTimeAgo;
    getGlobalStats(): {
        total_sessions: number;
        total_messages: number;
        active_sessions_last_hour: number;
        memory_usage_mb: number;
    };
}
//# sourceMappingURL=MemoryService.d.ts.map