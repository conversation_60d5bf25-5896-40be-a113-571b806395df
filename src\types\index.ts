// Core types for the AI Agent system

export interface AgentMessage {
  message: string;
  session_id: string;
}

export interface AgentResponse {
  response: string;
  session_id: string;
  timestamp: string;
  sources_used?: string[] | undefined;
  plugins_executed?: string[] | undefined;
}

export interface SessionMemory {
  session_id: string;
  messages: ChatMessage[];
  created_at: string;
  last_updated: string;
}

export interface ChatMessage {
  role: 'user' | 'assistant';
  content: string;
  timestamp: string;
}

export interface DocumentChunk {
  id: string;
  content: string;
  source: string;
  embedding?: number[];
  metadata?: Record<string, any>;
}

export interface RAGContext {
  chunks: DocumentChunk[];
  query: string;
  similarity_scores: number[];
}

export interface PluginResult {
  plugin_name: string;
  result: any;
  success: boolean;
  error?: string;
}

export interface Plugin {
  name: string;
  description: string;
  execute: (input: string) => Promise<PluginResult>;
  shouldExecute: (input: string) => boolean;
}

export interface SystemPromptContext {
  user_message: string;
  session_memory: ChatMessage[];
  rag_context: RAGContext | null;
  plugin_results: PluginResult[];
}

export interface EmbeddingResponse {
  embedding: number[];
  text: string;
}

export interface WeatherData {
  temperature: number;
  description: string;
  humidity: number;
  wind_speed: number;
  location: string;
}

export interface MathResult {
  expression: string;
  result: number;
  steps?: string[];
}
