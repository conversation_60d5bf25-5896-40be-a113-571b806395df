{"version": 3, "file": "MathPlugin.js", "sourceRoot": "", "sources": ["../../src/plugins/MathPlugin.ts"], "names": [], "mappings": ";;;AAEA,MAAa,UAAU;IAAvB;QACE,SAAI,GAAG,MAAM,CAAC;QACd,gBAAW,GAAG,4DAA4D,CAAC;IA6M7E,CAAC;IAxMC,aAAa,CAAC,KAAa;QACzB,MAAM,UAAU,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC;QAGvC,MAAM,YAAY,GAAG;YACnB,WAAW,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,YAAY;YACjE,KAAK,EAAE,UAAU,EAAE,UAAU,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO;YACjE,QAAQ,EAAE,OAAO,EAAE,SAAS,EAAE,UAAU;SACzC,CAAC;QAGF,MAAM,YAAY,GAAG;YACnB,0BAA0B;YAC1B,kBAAkB;YAClB,cAAc;YACd,QAAQ;YACR,mBAAmB;YACnB,YAAY;YACZ,SAAS;SACV,CAAC;QAEF,MAAM,UAAU,GAAG,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;QAC9E,MAAM,UAAU,GAAG,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;QAGrE,MAAM,oBAAoB,GAAG,mCAAmC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAE7E,OAAO,UAAU,IAAI,UAAU,IAAI,oBAAoB,CAAC;IAC1D,CAAC;IAKD,KAAK,CAAC,OAAO,CAAC,KAAa;QACzB,IAAI,CAAC;YACH,OAAO,CAAC,GAAG,CAAC,sCAAsC,KAAK,GAAG,CAAC,CAAC;YAE5D,MAAM,UAAU,GAAG,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC;YAErD,IAAI,CAAC,UAAU,EAAE,CAAC;gBAChB,OAAO;oBACL,WAAW,EAAE,IAAI,CAAC,IAAI;oBACtB,MAAM,EAAE,IAAI;oBACZ,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,mDAAmD;iBAC3D,CAAC;YACJ,CAAC;YAED,MAAM,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC;YAEnD,OAAO;gBACL,WAAW,EAAE,IAAI,CAAC,IAAI;gBACtB,MAAM;gBACN,OAAO,EAAE,IAAI;aACd,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;YACxD,OAAO;gBACL,WAAW,EAAE,IAAI,CAAC,IAAI;gBACtB,MAAM,EAAE,IAAI;gBACZ,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,wBAAwB;aACzE,CAAC;QACJ,CAAC;IACH,CAAC;IAKO,qBAAqB,CAAC,KAAa;QAEzC,IAAI,OAAO,GAAG,KAAK;aAChB,WAAW,EAAE;aACb,OAAO,CAAC,eAAe,EAAE,EAAE,CAAC;aAC5B,OAAO,CAAC,eAAe,EAAE,EAAE,CAAC;aAC5B,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC;aAC1B,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC;aACxB,OAAO,CAAC,qBAAqB,EAAE,EAAE,CAAC;aAClC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;aAClB,IAAI,EAAE,CAAC;QAGV,MAAM,QAAQ,GAAG;YAEf,yBAAyB;YAEzB,gDAAgD;YAEhD,oDAAoD;SACrD,CAAC;QAEF,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;YAC/B,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YACrC,IAAI,KAAK,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC;gBACtB,IAAI,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;gBAG3B,IAAI,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,EAAE,CAAC;oBACjC,OAAO,IAAI,CAAC;gBACd,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;IAKO,iBAAiB,CAAC,IAAY;QAEpC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;YAAE,OAAO,KAAK,CAAC;QAGnC,IAAI,CAAC,2CAA2C,CAAC,IAAI,CAAC,IAAI,CAAC;YAAE,OAAO,KAAK,CAAC;QAG1E,MAAM,UAAU,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC;QACpD,MAAM,WAAW,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC;QACrD,IAAI,UAAU,KAAK,WAAW;YAAE,OAAO,KAAK,CAAC;QAE7C,OAAO,IAAI,CAAC;IACd,CAAC;IAKO,kBAAkB,CAAC,UAAkB;QAC3C,IAAI,CAAC;YAEH,IAAI,UAAU,GAAG,UAAU;iBACxB,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC;iBACnB,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC;iBACrB,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;iBAClB,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;YAGtB,UAAU,GAAG,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC,CAAC;YAGlD,MAAM,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;YAEzC,IAAI,OAAO,MAAM,KAAK,QAAQ,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;gBACpD,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;YAChD,CAAC;YAED,OAAO;gBACL,UAAU,EAAE,UAAU;gBACtB,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,OAAO,CAAC,GAAG,OAAO;gBAC9C,KAAK,EAAE,CAAC,GAAG,UAAU,MAAM,MAAM,EAAE,CAAC;aACrC,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,+BAA+B,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,oBAAoB,EAAE,CAAC,CAAC;QAClH,CAAC;IACH,CAAC;IAKO,mBAAmB,CAAC,IAAY;QAEtC,OAAO,IAAI;aACR,OAAO,CAAC,kBAAkB,EAAE,eAAe,CAAC;aAC5C,OAAO,CAAC,iBAAiB,EAAE,8BAA8B,CAAC;aAC1D,OAAO,CAAC,iBAAiB,EAAE,8BAA8B,CAAC;aAC1D,OAAO,CAAC,iBAAiB,EAAE,8BAA8B,CAAC;aAC1D,OAAO,CAAC,iBAAiB,EAAE,gBAAgB,CAAC;aAC5C,OAAO,CAAC,gBAAgB,EAAE,cAAc,CAAC;aACzC,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;IAC1B,CAAC;IAKO,QAAQ,CAAC,IAAY;QAE3B,MAAM,cAAc,GAAG,6GAA6G,CAAC;QAErI,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,wCAAwC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC;YACrF,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;QAC5D,CAAC;QAGD,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,IAAI,QAAQ,CAAC,MAAM,EAAE,yBAAyB,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;YAC5E,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;QACrD,CAAC;IACH,CAAC;IAKD,MAAM,CAAC,gBAAgB,CAAC,IAAgB;QACtC,OAAO;oBACS,IAAI,CAAC,UAAU;gBACnB,IAAI,CAAC,MAAM;EACzB,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,gBAAgB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;IAC7D,CAAC;CACF;AA/MD,gCA+MC"}