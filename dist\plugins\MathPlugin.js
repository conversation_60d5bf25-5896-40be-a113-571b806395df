"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MathPlugin = void 0;
class MathPlugin {
    constructor() {
        this.name = 'math';
        this.description = 'Evaluate mathematical expressions and perform calculations';
    }
    shouldExecute(input) {
        const lowerInput = input.toLowerCase();
        const mathKeywords = [
            'calculate', 'compute', 'solve', 'math', 'equation', 'expression',
            'add', 'subtract', 'multiply', 'divide', 'plus', 'minus', 'times',
            'equals', 'equal', 'what is', 'how much'
        ];
        const mathPatterns = [
            /\d+\s*[\+\-\*\/\^]\s*\d+/,
            /\d+\s*\*\*\s*\d+/,
            /\(\s*\d+.*\)/,
            /sqrt\(/,
            /sin\(|cos\(|tan\(/,
            /log\(|ln\(/,
            /\d+\s*%/,
        ];
        const hasKeyword = mathKeywords.some(keyword => lowerInput.includes(keyword));
        const hasPattern = mathPatterns.some(pattern => pattern.test(input));
        const simpleNumberQuestion = /what\s+is\s+\d+.*[\+\-\*\/].*\d+/i.test(input);
        return hasKeyword || hasPattern || simpleNumberQuestion;
    }
    async execute(input) {
        try {
            console.log(`[MATH] Executing math plugin for: "${input}"`);
            const expression = this.extractMathExpression(input);
            if (!expression) {
                return {
                    plugin_name: this.name,
                    result: null,
                    success: false,
                    error: 'Could not extract a valid mathematical expression'
                };
            }
            const result = this.evaluateExpression(expression);
            return {
                plugin_name: this.name,
                result,
                success: true
            };
        }
        catch (error) {
            console.error('[MATH] Plugin execution failed:', error);
            return {
                plugin_name: this.name,
                result: null,
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error occurred'
            };
        }
    }
    extractMathExpression(input) {
        let cleaned = input
            .toLowerCase()
            .replace(/what\s+is\s+/g, '')
            .replace(/calculate\s+/g, '')
            .replace(/compute\s+/g, '')
            .replace(/solve\s+/g, '')
            .replace(/how\s+much\s+is\s+/g, '')
            .replace(/\?/g, '')
            .trim();
        const patterns = [
            /[\d\.\s\+\-\*\/\(\)\^]+/,
            /[\d\.\s\+\-\*\/\(\)]+\*\*[\d\.\s\+\-\*\/\(\)]+/,
            /(sqrt|sin|cos|tan|log|ln)\([\d\.\s\+\-\*\/\(\)]+\)/
        ];
        for (const pattern of patterns) {
            const match = cleaned.match(pattern);
            if (match && match[0]) {
                let expr = match[0].trim();
                if (this.isValidExpression(expr)) {
                    return expr;
                }
            }
        }
        return null;
    }
    isValidExpression(expr) {
        if (!/\d/.test(expr))
            return false;
        if (!/[\+\-\*\/\^]|\*\*|sqrt|sin|cos|tan|log|ln/.test(expr))
            return false;
        const openParens = (expr.match(/\(/g) || []).length;
        const closeParens = (expr.match(/\)/g) || []).length;
        if (openParens !== closeParens)
            return false;
        return true;
    }
    evaluateExpression(expression) {
        try {
            let normalized = expression
                .replace(/\s+/g, '')
                .replace(/\*\*/g, '^')
                .replace(/×/g, '*')
                .replace(/÷/g, '/');
            normalized = this.handleMathFunctions(normalized);
            const result = this.safeEval(normalized);
            if (typeof result !== 'number' || !isFinite(result)) {
                throw new Error('Invalid calculation result');
            }
            return {
                expression: expression,
                result: Math.round(result * 1000000) / 1000000,
                steps: [`${expression} = ${result}`]
            };
        }
        catch (error) {
            throw new Error(`Cannot evaluate expression: ${error instanceof Error ? error.message : 'Invalid expression'}`);
        }
    }
    handleMathFunctions(expr) {
        return expr
            .replace(/sqrt\(([^)]+)\)/g, 'Math.sqrt($1)')
            .replace(/sin\(([^)]+)\)/g, 'Math.sin($1 * Math.PI / 180)')
            .replace(/cos\(([^)]+)\)/g, 'Math.cos($1 * Math.PI / 180)')
            .replace(/tan\(([^)]+)\)/g, 'Math.tan($1 * Math.PI / 180)')
            .replace(/log\(([^)]+)\)/g, 'Math.log10($1)')
            .replace(/ln\(([^)]+)\)/g, 'Math.log($1)')
            .replace(/\^/g, '**');
    }
    safeEval(expr) {
        const allowedPattern = /^[0-9+\-*/.() \t\nMath.sqrt\(\)Math.sin\(\)Math.cos\(\)Math.tan\(\)Math.log10\(\)Math.log\(\)Math.PI\*\*]+$/;
        if (!allowedPattern.test(expr.replace(/Math\.(sqrt|sin|cos|tan|log10|log|PI)/g, ''))) {
            throw new Error('Expression contains invalid characters');
        }
        try {
            const result = new Function('Math', `"use strict"; return (${expr})`)(Math);
            return result;
        }
        catch (error) {
            throw new Error('Invalid mathematical expression');
        }
    }
    static formatMathResult(data) {
        return `🧮 **Math Calculation**
- **Expression**: ${data.expression}
- **Result**: ${data.result}
${data.steps ? `- **Steps**: ${data.steps.join(' → ')}` : ''}`;
    }
}
exports.MathPlugin = MathPlugin;
//# sourceMappingURL=MathPlugin.js.map