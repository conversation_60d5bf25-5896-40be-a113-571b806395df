# Server Configuration
PORT=3000
NODE_ENV=development

# OpenAI Configuration (REQUIRED)
OPENAI_API_KEY=sk-your-openai-api-key-here
OPENAI_MODEL=gpt-4

# Weather API Configuration (OPTIONAL - will use mock data if not provided)
WEATHER_API_KEY=your_weather_api_key_here
WEATHER_API_URL=https://api.openweathermap.org/data/2.5

# CORS Configuration
CORS_ORIGIN=*

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Memory Configuration
MAX_MEMORY_MESSAGES=10
SESSION_TIMEOUT_HOURS=24

# RAG Configuration
MAX_CHUNKS_PER_QUERY=3
CHUNK_SIZE=500
CHUNK_OVERLAP=50
