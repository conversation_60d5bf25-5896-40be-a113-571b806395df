import { Plugin, PluginResult, WeatherData } from '../types';
export declare class WeatherPlugin implements Plugin {
    name: string;
    description: string;
    private apiKey;
    private apiUrl;
    constructor();
    shouldExecute(input: string): boolean;
    execute(input: string): Promise<PluginResult>;
    private extractLocation;
    private getWeatherData;
    private getMockWeatherData;
    static formatWeatherData(data: WeatherData): string;
}
//# sourceMappingURL=WeatherPlugin.d.ts.map