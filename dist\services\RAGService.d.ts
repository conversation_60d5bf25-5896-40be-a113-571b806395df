import { RAGContext } from '../types';
export declare class RAGService {
    private documentService;
    constructor();
    initialize(): Promise<void>;
    retrieveContext(query: string): Promise<RAGContext | null>;
    private calculateSimilarityScores;
    formatContextForPrompt(context: RAGContext | null): string;
    getKnowledgeBaseStats(): Promise<{
        total_chunks: number;
        sources: string[];
        initialized: boolean;
    }>;
    shouldUseRAG(query: string): boolean;
}
//# sourceMappingURL=RAGService.d.ts.map