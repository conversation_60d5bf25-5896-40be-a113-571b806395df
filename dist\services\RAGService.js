"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RAGService = void 0;
const DocumentService_1 = require("./DocumentService");
class RAGService {
    constructor() {
        this.documentService = new DocumentService_1.DocumentService();
    }
    async initialize() {
        await this.documentService.initialize();
        console.log('[RAG] RAG service initialized successfully');
    }
    async retrieveContext(query) {
        try {
            const maxChunks = parseInt(process.env.MAX_CHUNKS_PER_QUERY || '3');
            console.log(`[RAG] Retrieving context for query: "${query.substring(0, 100)}..."`);
            const relevantChunks = await this.documentService.searchRelevantChunks(query, maxChunks);
            if (relevantChunks.length === 0) {
                console.log('[RAG] No relevant chunks found');
                return null;
            }
            const similarityScores = await this.calculateSimilarityScores(query, relevantChunks);
            const context = {
                chunks: relevantChunks,
                query,
                similarity_scores: similarityScores
            };
            console.log(`[RAG] Retrieved ${relevantChunks.length} relevant chunks`);
            relevantChunks.forEach((chunk, index) => {
                console.log(`[RAG] Chunk ${index + 1}: ${chunk.source} (${chunk.content.length} chars)`);
            });
            return context;
        }
        catch (error) {
            console.error('[RAG] Error retrieving context:', error);
            return null;
        }
    }
    async calculateSimilarityScores(query, chunks) {
        return chunks.map((_, index) => 0.9 - (index * 0.1));
    }
    formatContextForPrompt(context) {
        if (!context || context.chunks.length === 0) {
            return 'No relevant context found in the knowledge base.';
        }
        let formattedContext = 'Relevant information from knowledge base:\n\n';
        context.chunks.forEach((chunk, index) => {
            formattedContext += `**Source ${index + 1}: ${chunk.source}**\n`;
            formattedContext += `${chunk.content}\n\n`;
        });
        formattedContext += `Query: "${context.query}"\n`;
        formattedContext += `Retrieved ${context.chunks.length} relevant chunks.\n`;
        return formattedContext;
    }
    async getKnowledgeBaseStats() {
        try {
            await this.documentService.initialize();
            return {
                total_chunks: this.documentService.getTotalChunks(),
                sources: this.documentService.getAvailableSources(),
                initialized: true
            };
        }
        catch (error) {
            console.error('[RAG] Error getting knowledge base stats:', error);
            return {
                total_chunks: 0,
                sources: [],
                initialized: false
            };
        }
    }
    shouldUseRAG(query) {
        const lowerQuery = query.toLowerCase();
        const ragKeywords = [
            'markdown', 'blog', 'documentation', 'writing', 'format', 'syntax',
            'guide', 'tutorial', 'how to', 'what is', 'explain', 'tell me about',
            'jekyll', 'hugo', 'static site', 'cms', 'wordpress', 'typora',
            'editor', 'lightweight markup', 'restructuredtext', 'asciidoc'
        ];
        const hasRelevantKeywords = ragKeywords.some(keyword => lowerQuery.includes(keyword));
        const isQuestion = query.trim().endsWith('?');
        const isLongQuery = query.length > 20;
        return hasRelevantKeywords || isQuestion || isLongQuery;
    }
}
exports.RAGService = RAGService;
//# sourceMappingURL=RAGService.js.map