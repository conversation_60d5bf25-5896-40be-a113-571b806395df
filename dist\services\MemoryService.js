"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MemoryService = void 0;
class MemoryService {
    constructor() {
        this.sessions = new Map();
        this.maxMessages = parseInt(process.env.MAX_MEMORY_MESSAGES || '10');
        this.sessionTimeoutHours = parseInt(process.env.SESSION_TIMEOUT_HOURS || '24');
        setInterval(() => this.cleanupExpiredSessions(), 60 * 60 * 1000);
    }
    getSessionMemory(sessionId) {
        let session = this.sessions.get(sessionId);
        if (!session) {
            session = {
                session_id: sessionId,
                messages: [],
                created_at: new Date().toISOString(),
                last_updated: new Date().toISOString()
            };
            this.sessions.set(sessionId, session);
            console.log(`[MEMORY] Created new session: ${sessionId}`);
        }
        return session;
    }
    addMessage(sessionId, role, content) {
        const session = this.getSessionMemory(sessionId);
        const message = {
            role,
            content,
            timestamp: new Date().toISOString()
        };
        session.messages.push(message);
        session.last_updated = new Date().toISOString();
        if (session.messages.length > this.maxMessages) {
            session.messages = session.messages.slice(-this.maxMessages);
        }
        console.log(`[MEMORY] Added ${role} message to session ${sessionId} (${session.messages.length} total messages)`);
    }
    getRecentMessages(sessionId, count = 4) {
        const session = this.getSessionMemory(sessionId);
        return session.messages.slice(-count);
    }
    getConversationSummary(sessionId) {
        const session = this.getSessionMemory(sessionId);
        if (session.messages.length === 0) {
            return 'This is the start of a new conversation.';
        }
        const recentMessages = this.getRecentMessages(sessionId, 4);
        let summary = `Recent conversation history (${recentMessages.length} messages):\n\n`;
        recentMessages.forEach((message, index) => {
            const timeAgo = this.getTimeAgo(message.timestamp);
            summary += `${message.role.toUpperCase()} (${timeAgo}): ${message.content}\n`;
        });
        return summary;
    }
    clearSession(sessionId) {
        this.sessions.delete(sessionId);
        console.log(`[MEMORY] Cleared session: ${sessionId}`);
    }
    getActiveSessions() {
        return Array.from(this.sessions.keys());
    }
    getSessionStats(sessionId) {
        const session = this.getSessionMemory(sessionId);
        const createdAt = new Date(session.created_at);
        const now = new Date();
        const ageHours = (now.getTime() - createdAt.getTime()) / (1000 * 60 * 60);
        return {
            message_count: session.messages.length,
            created_at: session.created_at,
            last_updated: session.last_updated,
            session_age_hours: Math.round(ageHours * 100) / 100
        };
    }
    cleanupExpiredSessions() {
        const now = new Date();
        const expiredSessions = [];
        this.sessions.forEach((session, sessionId) => {
            const lastUpdated = new Date(session.last_updated);
            const hoursInactive = (now.getTime() - lastUpdated.getTime()) / (1000 * 60 * 60);
            if (hoursInactive > this.sessionTimeoutHours) {
                expiredSessions.push(sessionId);
            }
        });
        expiredSessions.forEach(sessionId => {
            this.sessions.delete(sessionId);
        });
        if (expiredSessions.length > 0) {
            console.log(`[MEMORY] Cleaned up ${expiredSessions.length} expired sessions`);
        }
    }
    getTimeAgo(timestamp) {
        const now = new Date();
        const messageTime = new Date(timestamp);
        const diffMs = now.getTime() - messageTime.getTime();
        const diffMinutes = Math.floor(diffMs / (1000 * 60));
        if (diffMinutes < 1)
            return 'just now';
        if (diffMinutes < 60)
            return `${diffMinutes}m ago`;
        const diffHours = Math.floor(diffMinutes / 60);
        if (diffHours < 24)
            return `${diffHours}h ago`;
        const diffDays = Math.floor(diffHours / 24);
        return `${diffDays}d ago`;
    }
    getGlobalStats() {
        const now = new Date();
        const oneHourAgo = new Date(now.getTime() - (60 * 60 * 1000));
        let totalMessages = 0;
        let activeLastHour = 0;
        this.sessions.forEach(session => {
            totalMessages += session.messages.length;
            const lastUpdated = new Date(session.last_updated);
            if (lastUpdated > oneHourAgo) {
                activeLastHour++;
            }
        });
        const memoryUsageMb = (JSON.stringify(Array.from(this.sessions.values())).length / 1024 / 1024);
        return {
            total_sessions: this.sessions.size,
            total_messages: totalMessages,
            active_sessions_last_hour: activeLastHour,
            memory_usage_mb: Math.round(memoryUsageMb * 100) / 100
        };
    }
}
exports.MemoryService = MemoryService;
//# sourceMappingURL=MemoryService.js.map