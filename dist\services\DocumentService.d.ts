import { DocumentChunk } from '../types';
export declare class DocumentService {
    private embeddingService;
    private chunks;
    private isInitialized;
    constructor();
    initialize(): Promise<void>;
    private loadAndProcessDocuments;
    private processDocument;
    private chunkDocument;
    searchRelevantChunks(query: string, maxChunks?: number): Promise<DocumentChunk[]>;
    getAvailableSources(): string[];
    getTotalChunks(): number;
}
//# sourceMappingURL=DocumentService.d.ts.map