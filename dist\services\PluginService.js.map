{"version": 3, "file": "PluginService.js", "sourceRoot": "", "sources": ["../../src/services/PluginService.ts"], "names": [], "mappings": ";;;AACA,4DAAyD;AACzD,sDAAmD;AAEnD,MAAa,aAAa;IAGxB;QAFQ,YAAO,GAAa,EAAE,CAAC;QAG7B,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAC3B,CAAC;IAKO,iBAAiB;QACvB,IAAI,CAAC,OAAO,GAAG;YACb,IAAI,6BAAa,EAAE;YACnB,IAAI,uBAAU,EAAE;SACjB,CAAC;QAEF,OAAO,CAAC,GAAG,CAAC,yBAAyB,IAAI,CAAC,OAAO,CAAC,MAAM,aAAa,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;IACnH,CAAC;IAKD,KAAK,CAAC,cAAc,CAAC,KAAa;QAChC,MAAM,OAAO,GAAmB,EAAE,CAAC;QAEnC,OAAO,CAAC,GAAG,CAAC,0CAA0C,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC;QAGrF,MAAM,gBAAgB,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;YACpD,MAAM,aAAa,GAAG,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;YAClD,IAAI,aAAa,EAAE,CAAC;gBAClB,OAAO,CAAC,GAAG,CAAC,qBAAqB,MAAM,CAAC,IAAI,gBAAgB,CAAC,CAAC;YAChE,CAAC;YACD,OAAO,aAAa,CAAC;QACvB,CAAC,CAAC,CAAC;QAEH,IAAI,gBAAgB,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAClC,OAAO,CAAC,GAAG,CAAC,+CAA+C,CAAC,CAAC;YAC7D,OAAO,OAAO,CAAC;QACjB,CAAC;QAGD,MAAM,cAAc,GAAG,gBAAgB,CAAC,GAAG,CAAC,KAAK,EAAC,MAAM,EAAC,EAAE;YACzD,IAAI,CAAC;gBACH,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;gBAC7B,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;gBAC3C,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;gBAExC,OAAO,CAAC,GAAG,CAAC,qBAAqB,MAAM,CAAC,IAAI,iBAAiB,QAAQ,iBAAiB,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC;gBACxG,OAAO,MAAM,CAAC;YAChB,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,qBAAqB,MAAM,CAAC,IAAI,WAAW,EAAE,KAAK,CAAC,CAAC;gBAClE,OAAO;oBACL,WAAW,EAAE,MAAM,CAAC,IAAI;oBACxB,MAAM,EAAE,IAAI;oBACZ,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,sBAAsB;iBACvD,CAAC;YACpB,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC;YACH,MAAM,aAAa,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;YACxD,OAAO,CAAC,IAAI,CAAC,GAAG,aAAa,CAAC,CAAC;QACjC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,oCAAoC,EAAE,KAAK,CAAC,CAAC;QAC7D,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAKD,mBAAmB;QACjB,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;YACjC,IAAI,EAAE,MAAM,CAAC,IAAI;YACjB,WAAW,EAAE,MAAM,CAAC,WAAW;SAChC,CAAC,CAAC,CAAC;IACN,CAAC;IAKD,mBAAmB,CAAC,KAAa;QAC/B,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC;IAClE,CAAC;IAKD,SAAS,CAAC,IAAY;QACpB,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC;IAC3D,CAAC;IAKD,mBAAmB,CAAC,OAAuB;QACzC,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACzB,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,IAAI,SAAS,GAAG,2BAA2B,CAAC;QAE5C,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YACvB,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;gBACnB,QAAQ,MAAM,CAAC,WAAW,EAAE,CAAC;oBAC3B,KAAK,SAAS;wBACZ,SAAS,IAAI,6BAAa,CAAC,iBAAiB,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC;wBACrE,MAAM;oBACR,KAAK,MAAM;wBACT,SAAS,IAAI,uBAAU,CAAC,gBAAgB,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC;wBACjE,MAAM;oBACR;wBACE,SAAS,IAAI,KAAK,MAAM,CAAC,WAAW,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC;gBACnF,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,SAAS,IAAI,KAAK,MAAM,CAAC,WAAW,SAAS,MAAM,CAAC,KAAK,MAAM,CAAC;YAClE,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,OAAO,SAAS,CAAC;IACnB,CAAC;IAKD,cAAc;QAKZ,OAAO;YACL,aAAa,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM;YAClC,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;YAC3C,YAAY,EAAE,IAAI,CAAC,mBAAmB,EAAE;SACzC,CAAC;IACJ,CAAC;IAKD,KAAK,CAAC,cAAc;QAClB,MAAM,UAAU,GAAqC;YACnD,OAAO,EAAE,gCAAgC;YACzC,IAAI,EAAE,qBAAqB;SAC5B,CAAC;QAEF,MAAM,OAAO,GAAsC,EAAE,CAAC;QAEtD,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YAClC,MAAM,SAAS,GAAG,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YAC1C,IAAI,SAAS,EAAE,CAAC;gBACd,IAAI,CAAC;oBACH,MAAM,aAAa,GAAG,MAAM,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;oBACtD,IAAI,aAAa,EAAE,CAAC;wBAClB,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;wBAC/C,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,OAAO,CAAC;oBACxC,CAAC;yBAAM,CAAC;wBACN,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;oBAC/B,CAAC;gBACH,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,OAAO,CAAC,KAAK,CAAC,6BAA6B,MAAM,CAAC,IAAI,GAAG,EAAE,KAAK,CAAC,CAAC;oBAClE,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;gBAC/B,CAAC;YACH,CAAC;iBAAM,CAAC;gBACN,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;YAC/B,CAAC;QACH,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;CACF;AA7KD,sCA6KC"}