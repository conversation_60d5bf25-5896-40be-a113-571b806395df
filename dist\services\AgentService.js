"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AgentService = void 0;
const MemoryService_1 = require("./MemoryService");
const RAGService_1 = require("./RAGService");
const PluginService_1 = require("./PluginService");
const LLMService_1 = require("./LLMService");
class AgentService {
    constructor() {
        this.isInitialized = false;
        this.memoryService = new MemoryService_1.MemoryService();
        this.ragService = new RAGService_1.RAGService();
        this.pluginService = new PluginService_1.PluginService();
        this.llmService = new LLMService_1.LLMService();
    }
    async initialize() {
        if (this.isInitialized) {
            return;
        }
        console.log('[AGENT] Initializing agent service...');
        try {
            await this.ragService.initialize();
            const llmConnected = await this.llmService.testConnection();
            if (!llmConnected) {
                console.warn('[AGENT] LLM connection test failed, but continuing...');
            }
            this.isInitialized = true;
            console.log('[AGENT] Agent service initialized successfully');
        }
        catch (error) {
            console.error('[AGENT] Failed to initialize agent service:', error);
            throw error;
        }
    }
    async processMessage(message, sessionId) {
        const startTime = Date.now();
        try {
            await this.initialize();
            console.log(`[AGENT] Processing message for session ${sessionId}`);
            this.memoryService.addMessage(sessionId, 'user', message);
            const sessionMemory = this.memoryService.getRecentMessages(sessionId, 4);
            const shouldUseRAG = this.ragService.shouldUseRAG(message);
            let ragContext = null;
            if (shouldUseRAG) {
                console.log('[AGENT] Using RAG for this query');
                ragContext = await this.ragService.retrieveContext(message);
            }
            else {
                console.log('[AGENT] Skipping RAG for this query');
            }
            const pluginResults = await this.pluginService.executePlugins(message);
            const systemContext = {
                user_message: message,
                session_memory: sessionMemory,
                rag_context: ragContext,
                plugin_results: pluginResults
            };
            let llmResponse = await this.llmService.generateResponse(systemContext);
            if (pluginResults.length > 0) {
                const pluginFormatted = this.pluginService.formatPluginResults(pluginResults);
                llmResponse += pluginFormatted;
            }
            this.memoryService.addMessage(sessionId, 'assistant', llmResponse);
            const response = {
                response: llmResponse,
                session_id: sessionId,
                timestamp: new Date().toISOString(),
                sources_used: ragContext ? ragContext.chunks.map(chunk => chunk.source) : undefined,
                plugins_executed: pluginResults.length > 0 ? pluginResults.map(p => p.plugin_name) : undefined
            };
            const duration = Date.now() - startTime;
            console.log(`[AGENT] Message processed successfully in ${duration}ms`);
            return response;
        }
        catch (error) {
            console.error('[AGENT] Error processing message:', error);
            const errorMessage = `I apologize, but I encountered an error while processing your request: ${error instanceof Error ? error.message : 'Unknown error'}`;
            this.memoryService.addMessage(sessionId, 'assistant', errorMessage);
            return {
                response: errorMessage,
                session_id: sessionId,
                timestamp: new Date().toISOString()
            };
        }
    }
    async getSessionMemory(sessionId) {
        return this.memoryService.getSessionMemory(sessionId);
    }
    async clearSessionMemory(sessionId) {
        this.memoryService.clearSession(sessionId);
    }
    async getHealthStatus() {
        const components = {
            memory: true,
            rag: false,
            plugins: false,
            llm: false
        };
        const stats = {
            memory: this.memoryService.getGlobalStats(),
            rag: null,
            plugins: null,
            llm: null
        };
        try {
            const ragStats = await this.ragService.getKnowledgeBaseStats();
            stats.rag = ragStats;
            components.rag = ragStats.initialized;
        }
        catch (error) {
            console.error('[AGENT] RAG health check failed:', error);
        }
        try {
            const pluginStats = this.pluginService.getPluginStats();
            stats.plugins = pluginStats;
            components.plugins = pluginStats.total_plugins > 0;
        }
        catch (error) {
            console.error('[AGENT] Plugin health check failed:', error);
        }
        try {
            components.llm = await this.llmService.testConnection();
            stats.llm = this.llmService.getModelInfo();
        }
        catch (error) {
            console.error('[AGENT] LLM health check failed:', error);
        }
        const healthyComponents = Object.values(components).filter(Boolean).length;
        const totalComponents = Object.keys(components).length;
        let status;
        if (healthyComponents === totalComponents) {
            status = 'healthy';
        }
        else if (healthyComponents >= totalComponents / 2) {
            status = 'degraded';
        }
        else {
            status = 'unhealthy';
        }
        return {
            status,
            components,
            stats
        };
    }
    getAgentInfo() {
        return {
            name: 'AI Agent Server',
            version: '1.0.0',
            capabilities: [
                'Natural language conversation with memory',
                'Retrieval-Augmented Generation (RAG) from markdown knowledge base',
                'Weather information via plugin',
                'Mathematical calculations via plugin',
                'Session-based conversation memory',
                'Multi-turn dialogue support'
            ],
            plugins: this.pluginService.getAvailablePlugins(),
            knowledge_base: [
                'Markdown blogging and documentation',
                'Static site generators (Jekyll, Hugo)',
                'Lightweight markup languages',
                'Web development tools and workflows',
                'Content management systems'
            ]
        };
    }
}
exports.AgentService = AgentService;
//# sourceMappingURL=AgentService.js.map