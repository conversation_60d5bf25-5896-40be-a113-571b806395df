import { AgentResponse, SystemPromptContext, SessionMemory } from '../types';
import { MemoryService } from './MemoryService';
import { RAGService } from './RAGService';
import { PluginService } from './PluginService';
import { LLMService } from './LLMService';

export class AgentService {
  private memoryService: MemoryService;
  private ragService: RAGService;
  private pluginService: PluginService;
  private llmService: LLMService;
  private isInitialized = false;

  constructor() {
    this.memoryService = new MemoryService();
    this.ragService = new RAGService();
    this.pluginService = new PluginService();
    this.llmService = new LLMService();
  }

  /**
   * Initialize the agent service
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    console.log('[AGENT] Initializing agent service...');
    
    try {
      // Initialize RAG service (loads and processes documents)
      await this.ragService.initialize();
      
      // Test LLM connection
      const llmConnected = await this.llmService.testConnection();
      if (!llmConnected) {
        console.warn('[AGENT] LLM connection test failed, but continuing...');
      }

      this.isInitialized = true;
      console.log('[AGENT] Agent service initialized successfully');
    } catch (error) {
      console.error('[AGENT] Failed to initialize agent service:', error);
      throw error;
    }
  }

  /**
   * Process a user message and generate a response
   */
  async processMessage(message: string, sessionId: string): Promise<AgentResponse> {
    const startTime = Date.now();
    
    try {
      // Ensure agent is initialized
      await this.initialize();

      console.log(`[AGENT] Processing message for session ${sessionId}`);

      // Step 1: Add user message to memory
      this.memoryService.addMessage(sessionId, 'user', message);

      // Step 2: Get session memory for context
      const sessionMemory = this.memoryService.getRecentMessages(sessionId, 4);

      // Step 3: Determine if we should use RAG
      const shouldUseRAG = this.ragService.shouldUseRAG(message);
      let ragContext = null;
      
      if (shouldUseRAG) {
        console.log('[AGENT] Using RAG for this query');
        ragContext = await this.ragService.retrieveContext(message);
      } else {
        console.log('[AGENT] Skipping RAG for this query');
      }

      // Step 4: Execute relevant plugins
      const pluginResults = await this.pluginService.executePlugins(message);

      // Step 5: Build system prompt context
      const systemContext: SystemPromptContext = {
        user_message: message,
        session_memory: sessionMemory,
        rag_context: ragContext,
        plugin_results: pluginResults
      };

      // Step 6: Generate LLM response
      let llmResponse = await this.llmService.generateResponse(systemContext);

      // Step 7: Append plugin results to response if any
      if (pluginResults.length > 0) {
        const pluginFormatted = this.pluginService.formatPluginResults(pluginResults);
        llmResponse += pluginFormatted;
      }

      // Step 8: Add assistant response to memory
      this.memoryService.addMessage(sessionId, 'assistant', llmResponse);

      // Step 9: Build response object
      const response: AgentResponse = {
        response: llmResponse,
        session_id: sessionId,
        timestamp: new Date().toISOString(),
        sources_used: ragContext ? ragContext.chunks.map(chunk => chunk.source) : undefined,
        plugins_executed: pluginResults.length > 0 ? pluginResults.map(p => p.plugin_name) : undefined
      };

      const duration = Date.now() - startTime;
      console.log(`[AGENT] Message processed successfully in ${duration}ms`);

      return response;

    } catch (error) {
      console.error('[AGENT] Error processing message:', error);
      
      // Add error to memory for context
      const errorMessage = `I apologize, but I encountered an error while processing your request: ${error instanceof Error ? error.message : 'Unknown error'}`;
      this.memoryService.addMessage(sessionId, 'assistant', errorMessage);

      return {
        response: errorMessage,
        session_id: sessionId,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Get session memory for debugging
   */
  async getSessionMemory(sessionId: string): Promise<SessionMemory> {
    return this.memoryService.getSessionMemory(sessionId);
  }

  /**
   * Clear session memory
   */
  async clearSessionMemory(sessionId: string): Promise<void> {
    this.memoryService.clearSession(sessionId);
  }

  /**
   * Get agent health status
   */
  async getHealthStatus(): Promise<{
    status: 'healthy' | 'degraded' | 'unhealthy';
    components: {
      memory: boolean;
      rag: boolean;
      plugins: boolean;
      llm: boolean;
    };
    stats: {
      memory: any;
      rag: any;
      plugins: any;
      llm: any;
    };
  }> {
    const components = {
      memory: true, // Memory service is always available
      rag: false,
      plugins: false,
      llm: false
    };

    const stats = {
      memory: this.memoryService.getGlobalStats(),
      rag: null as any,
      plugins: null as any,
      llm: null as any
    };

    try {
      // Test RAG service
      const ragStats = await this.ragService.getKnowledgeBaseStats();
      stats.rag = ragStats;
      components.rag = ragStats.initialized;
    } catch (error) {
      console.error('[AGENT] RAG health check failed:', error);
    }

    try {
      // Test plugin service
      const pluginStats = this.pluginService.getPluginStats();
      stats.plugins = pluginStats;
      components.plugins = pluginStats.total_plugins > 0;
    } catch (error) {
      console.error('[AGENT] Plugin health check failed:', error);
    }

    try {
      // Test LLM service
      components.llm = await this.llmService.testConnection();
      stats.llm = this.llmService.getModelInfo();
    } catch (error) {
      console.error('[AGENT] LLM health check failed:', error);
    }

    const healthyComponents = Object.values(components).filter(Boolean).length;
    const totalComponents = Object.keys(components).length;

    let status: 'healthy' | 'degraded' | 'unhealthy';
    if (healthyComponents === totalComponents) {
      status = 'healthy';
    } else if (healthyComponents >= totalComponents / 2) {
      status = 'degraded';
    } else {
      status = 'unhealthy';
    }

    return {
      status,
      components,
      stats
    };
  }

  /**
   * Get agent capabilities and information
   */
  getAgentInfo(): {
    name: string;
    version: string;
    capabilities: string[];
    plugins: Array<{ name: string; description: string }>;
    knowledge_base: string[];
  } {
    return {
      name: 'AI Agent Server',
      version: '1.0.0',
      capabilities: [
        'Natural language conversation with memory',
        'Retrieval-Augmented Generation (RAG) from markdown knowledge base',
        'Weather information via plugin',
        'Mathematical calculations via plugin',
        'Session-based conversation memory',
        'Multi-turn dialogue support'
      ],
      plugins: this.pluginService.getAvailablePlugins(),
      knowledge_base: [
        'Markdown blogging and documentation',
        'Static site generators (Jekyll, Hugo)',
        'Lightweight markup languages',
        'Web development tools and workflows',
        'Content management systems'
      ]
    };
  }
}
