import { Plugin, PluginResult } from '../types';
export declare class PluginService {
    private plugins;
    constructor();
    private initializePlugins;
    executePlugins(input: string): Promise<PluginResult[]>;
    getAvailablePlugins(): Array<{
        name: string;
        description: string;
    }>;
    wouldExecutePlugins(input: string): boolean;
    getPlugin(name: string): Plugin | undefined;
    formatPluginResults(results: PluginResult[]): string;
    getPluginStats(): {
        total_plugins: number;
        plugin_names: string[];
        plugins_info: Array<{
            name: string;
            description: string;
        }>;
    };
    testAllPlugins(): Promise<{
        [pluginName: string]: boolean;
    }>;
}
//# sourceMappingURL=PluginService.d.ts.map