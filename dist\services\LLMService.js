"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LLMService = void 0;
const openai_1 = __importDefault(require("openai"));
class LLMService {
    constructor() {
        if (!process.env.OPENAI_API_KEY) {
            throw new Error('OPENAI_API_KEY environment variable is required');
        }
        this.openai = new openai_1.default({
            apiKey: process.env.OPENAI_API_KEY,
        });
        this.model = process.env.OPENAI_MODEL || 'gpt-4';
    }
    async generateResponse(context) {
        try {
            const systemPrompt = this.buildSystemPrompt(context);
            console.log(`[LLM] Generating response using model: ${this.model}`);
            console.log(`[LLM] System prompt length: ${systemPrompt.length} characters`);
            const response = await this.openai.chat.completions.create({
                model: this.model,
                messages: [
                    {
                        role: 'system',
                        content: systemPrompt
                    },
                    {
                        role: 'user',
                        content: context.user_message
                    }
                ],
                temperature: 0.7,
                max_tokens: 1500,
                top_p: 0.9,
                frequency_penalty: 0.1,
                presence_penalty: 0.1
            });
            const assistantMessage = response.choices[0]?.message?.content;
            if (!assistantMessage) {
                throw new Error('No response generated from OpenAI');
            }
            console.log(`[LLM] Generated response: ${assistantMessage.length} characters`);
            return assistantMessage;
        }
        catch (error) {
            console.error('[LLM] Error generating response:', error);
            if (error instanceof Error) {
                if (error.message.includes('rate limit')) {
                    throw new Error('Rate limit exceeded. Please try again in a moment.');
                }
                else if (error.message.includes('insufficient_quota')) {
                    throw new Error('OpenAI API quota exceeded. Please check your billing.');
                }
                else if (error.message.includes('invalid_api_key')) {
                    throw new Error('Invalid OpenAI API key. Please check your configuration.');
                }
            }
            throw new Error(`Failed to generate response: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    buildSystemPrompt(context) {
        let prompt = `You are an intelligent AI assistant with access to a knowledge base about markdown, blogging, documentation, and web development. You can also execute plugins to provide additional functionality.

## Your Capabilities:
1. **Knowledge Base Access**: You have access to comprehensive information about markdown, blogging platforms, static site generators, and web development tools.
2. **Plugin Execution**: You can execute plugins for weather information and mathematical calculations.
3. **Memory**: You maintain conversation history to provide contextual responses.

## Response Guidelines:
- Be helpful, accurate, and concise
- Use information from the knowledge base when relevant
- Acknowledge when plugin results are included in your response
- If you don't know something, say so honestly
- Format your responses clearly with markdown when appropriate

`;
        if (context.session_memory.length > 0) {
            prompt += `## Conversation History:
`;
            context.session_memory.forEach(message => {
                prompt += `**${message.role.toUpperCase()}**: ${message.content}\n`;
            });
            prompt += '\n';
        }
        else {
            prompt += `## Conversation History:
This is the start of a new conversation.

`;
        }
        if (context.rag_context && context.rag_context.chunks.length > 0) {
            prompt += `## Knowledge Base Context:
The following information from the knowledge base is relevant to the user's query:

`;
            context.rag_context.chunks.forEach((chunk, index) => {
                prompt += `**Source ${index + 1}: ${chunk.source}**
${chunk.content}

`;
            });
        }
        if (context.plugin_results.length > 0) {
            prompt += `## Plugin Results:
The following plugins were executed for this query:

`;
            context.plugin_results.forEach(result => {
                if (result.success) {
                    prompt += `**${result.plugin_name}**: ${JSON.stringify(result.result)}
`;
                }
                else {
                    prompt += `**${result.plugin_name}**: Error - ${result.error}
`;
                }
            });
            prompt += '\n';
        }
        prompt += `## Current User Message:
"${context.user_message}"

Please provide a helpful response based on the above context, knowledge base information, and any plugin results. Be natural and conversational while being informative.`;
        return prompt;
    }
    async testConnection() {
        try {
            const response = await this.openai.chat.completions.create({
                model: this.model,
                messages: [
                    {
                        role: 'user',
                        content: 'Hello, please respond with "Connection successful"'
                    }
                ],
                max_tokens: 10
            });
            const message = response.choices[0]?.message?.content;
            return message?.includes('Connection successful') || message?.includes('successful') || false;
        }
        catch (error) {
            console.error('[LLM] Connection test failed:', error);
            return false;
        }
    }
    getModelInfo() {
        return {
            model: this.model,
            provider: 'OpenAI'
        };
    }
}
exports.LLMService = LLMService;
//# sourceMappingURL=LLMService.js.map