import { AgentResponse, SessionMemory } from '../types';
export declare class AgentService {
    private memoryService;
    private ragService;
    private pluginService;
    private llmService;
    private isInitialized;
    constructor();
    initialize(): Promise<void>;
    processMessage(message: string, sessionId: string): Promise<AgentResponse>;
    getSessionMemory(sessionId: string): Promise<SessionMemory>;
    clearSessionMemory(sessionId: string): Promise<void>;
    getHealthStatus(): Promise<{
        status: 'healthy' | 'degraded' | 'unhealthy';
        components: {
            memory: boolean;
            rag: boolean;
            plugins: boolean;
            llm: boolean;
        };
        stats: {
            memory: any;
            rag: any;
            plugins: any;
            llm: any;
        };
    }>;
    getAgentInfo(): {
        name: string;
        version: string;
        capabilities: string[];
        plugins: Array<{
            name: string;
            description: string;
        }>;
        knowledge_base: string[];
    };
}
//# sourceMappingURL=AgentService.d.ts.map