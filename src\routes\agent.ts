import { Router, Request, Response, NextFunction } from 'express';
import { AgentMessage, AgentResponse } from '../types';
import { AgentService } from '../services/AgentService';
import { createError } from '../middleware/errorHandler';

const router = Router();
let agentService: AgentService;

// Lazy initialization of AgentService
function getAgentService(): AgentService {
  if (!agentService) {
    agentService = new AgentService();
  }
  return agentService;
}

// Validation middleware for agent message
const validateAgentMessage = (req: Request, res: Response, next: NextFunction): void => {
  const { message, session_id } = req.body as AgentMessage;

  if (!message || typeof message !== 'string' || message.trim().length === 0) {
    throw createError('Message is required and must be a non-empty string', 400);
  }

  if (!session_id || typeof session_id !== 'string' || session_id.trim().length === 0) {
    throw createError('Session ID is required and must be a non-empty string', 400);
  }

  if (message.length > 4000) {
    throw createError('Message is too long. Maximum length is 4000 characters', 400);
  }

  next();
};

// POST /agent/message - Main agent endpoint
router.post('/message', validateAgentMessage, async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { message, session_id } = req.body as AgentMessage;

    console.log(`[AGENT] Processing message for session: ${session_id}`);
    console.log(`[AGENT] Message: ${message.substring(0, 100)}${message.length > 100 ? '...' : ''}`);

    const response: AgentResponse = await getAgentService().processMessage(message, session_id);

    res.json(response);
  } catch (error) {
    next(error);
  }
});

// GET /agent/sessions/:session_id/memory - Get session memory (for debugging)
router.get('/sessions/:session_id/memory', async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { session_id } = req.params;
    
    if (!session_id) {
      throw createError('Session ID is required', 400);
    }

    const memory = await getAgentService().getSessionMemory(session_id);
    
    res.json({
      session_id,
      memory,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    next(error);
  }
});

// DELETE /agent/sessions/:session_id - Clear session memory
router.delete('/sessions/:session_id', async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { session_id } = req.params;

    if (!session_id) {
      throw createError('Session ID is required', 400);
    }

    await getAgentService().clearSessionMemory(session_id);

    res.json({
      message: 'Session memory cleared successfully',
      session_id,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    next(error);
  }
});

// GET /agent/health - Agent health status
router.get('/health', async (req: Request, res: Response, next: NextFunction) => {
  try {
    const healthStatus = await getAgentService().getHealthStatus();

    res.status(healthStatus.status === 'healthy' ? 200 :
               healthStatus.status === 'degraded' ? 206 : 503)
       .json(healthStatus);
  } catch (error) {
    next(error);
  }
});

// GET /agent/info - Agent capabilities and information
router.get('/info', (req: Request, res: Response, next: NextFunction) => {
  try {
    const agentInfo = getAgentService().getAgentInfo();
    res.json(agentInfo);
  } catch (error) {
    next(error);
  }
});

export { router as agentRouter };
