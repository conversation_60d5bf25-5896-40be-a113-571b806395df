"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WeatherPlugin = void 0;
const axios_1 = __importDefault(require("axios"));
class WeatherPlugin {
    constructor() {
        this.name = 'weather';
        this.description = 'Get current weather information for a specified location';
        this.apiKey = process.env.WEATHER_API_KEY || '';
        this.apiUrl = process.env.WEATHER_API_URL || 'https://api.openweathermap.org/data/2.5';
        if (!this.apiKey) {
            console.warn('[WEATHER] No API key provided, using mock data');
        }
    }
    shouldExecute(input) {
        const lowerInput = input.toLowerCase();
        const weatherKeywords = [
            'weather', 'temperature', 'forecast', 'climate', 'rain', 'sunny', 'cloudy',
            'hot', 'cold', 'humidity', 'wind', 'storm', 'snow', 'degrees'
        ];
        const locationKeywords = [
            'in ', 'at ', 'for ', 'bangalore', 'mumbai', 'delhi', 'chennai', 'kolkata',
            'london', 'paris', 'tokyo', 'new york', 'san francisco', 'seattle'
        ];
        const hasWeatherKeyword = weatherKeywords.some(keyword => lowerInput.includes(keyword));
        const hasLocationKeyword = locationKeywords.some(keyword => lowerInput.includes(keyword));
        return hasWeatherKeyword && (hasLocationKeyword || lowerInput.includes('weather'));
    }
    async execute(input) {
        try {
            console.log(`[WEATHER] Executing weather plugin for: "${input}"`);
            const location = this.extractLocation(input);
            if (!location) {
                return {
                    plugin_name: this.name,
                    result: null,
                    success: false,
                    error: 'Could not extract location from the query'
                };
            }
            const weatherData = await this.getWeatherData(location);
            return {
                plugin_name: this.name,
                result: weatherData,
                success: true
            };
        }
        catch (error) {
            console.error('[WEATHER] Plugin execution failed:', error);
            return {
                plugin_name: this.name,
                result: null,
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error occurred'
            };
        }
    }
    extractLocation(input) {
        const lowerInput = input.toLowerCase();
        const patterns = [
            /weather (?:in|at|for) ([a-zA-Z\s]+?)(?:\?|$|,)/,
            /(?:in|at|for) ([a-zA-Z\s]+?) weather/,
            /temperature (?:in|at|for) ([a-zA-Z\s]+?)(?:\?|$|,)/,
            /([a-zA-Z\s]+?) weather/,
            /weather ([a-zA-Z\s]+?)(?:\?|$|,)/
        ];
        for (const pattern of patterns) {
            const match = lowerInput.match(pattern);
            if (match && match[1]) {
                return match[1].trim();
            }
        }
        const commonCities = [
            'bangalore', 'mumbai', 'delhi', 'chennai', 'kolkata', 'hyderabad',
            'london', 'paris', 'tokyo', 'new york', 'san francisco', 'seattle',
            'los angeles', 'chicago', 'boston', 'miami', 'sydney', 'melbourne'
        ];
        for (const city of commonCities) {
            if (lowerInput.includes(city)) {
                return city;
            }
        }
        return null;
    }
    async getWeatherData(location) {
        if (!this.apiKey) {
            return this.getMockWeatherData(location);
        }
        try {
            const response = await axios_1.default.get(`${this.apiUrl}/weather`, {
                params: {
                    q: location,
                    appid: this.apiKey,
                    units: 'metric'
                },
                timeout: 5000
            });
            const data = response.data;
            return {
                temperature: Math.round(data.main.temp),
                description: data.weather[0].description,
                humidity: data.main.humidity,
                wind_speed: data.wind.speed,
                location: `${data.name}, ${data.sys.country}`
            };
        }
        catch (error) {
            console.warn('[WEATHER] API call failed, using mock data:', error);
            return this.getMockWeatherData(location);
        }
    }
    getMockWeatherData(location) {
        const temperatures = [18, 22, 25, 28, 32, 15, 20];
        const descriptions = [
            'clear sky', 'few clouds', 'scattered clouds', 'broken clouds',
            'shower rain', 'rain', 'thunderstorm', 'snow', 'mist'
        ];
        const humidities = [45, 55, 65, 75, 85];
        const windSpeeds = [2.5, 3.2, 4.1, 5.8, 7.2];
        const randomTemp = temperatures[Math.floor(Math.random() * temperatures.length)];
        const randomDesc = descriptions[Math.floor(Math.random() * descriptions.length)];
        const randomHumidity = humidities[Math.floor(Math.random() * humidities.length)];
        const randomWind = windSpeeds[Math.floor(Math.random() * windSpeeds.length)];
        return {
            temperature: randomTemp,
            description: randomDesc,
            humidity: randomHumidity,
            wind_speed: randomWind,
            location: location.charAt(0).toUpperCase() + location.slice(1)
        };
    }
    static formatWeatherData(data) {
        return `🌤️ **Weather in ${data.location}**
- **Temperature**: ${data.temperature}°C
- **Condition**: ${data.description}
- **Humidity**: ${data.humidity}%
- **Wind Speed**: ${data.wind_speed} m/s`;
    }
}
exports.WeatherPlugin = WeatherPlugin;
//# sourceMappingURL=WeatherPlugin.js.map