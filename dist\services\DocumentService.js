"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DocumentService = void 0;
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
const EmbeddingService_1 = require("./EmbeddingService");
const uuid_1 = require("uuid");
class DocumentService {
    constructor() {
        this.chunks = [];
        this.isInitialized = false;
        this.embeddingService = new EmbeddingService_1.EmbeddingService();
    }
    async initialize() {
        if (this.isInitialized) {
            return;
        }
        console.log('[DOCS] Initializing document service...');
        try {
            await this.loadAndProcessDocuments();
            this.isInitialized = true;
            console.log(`[DOCS] Successfully initialized with ${this.chunks.length} chunks`);
        }
        catch (error) {
            console.error('[DOCS] Failed to initialize document service:', error);
            throw error;
        }
    }
    async loadAndProcessDocuments() {
        const documentsDir = path_1.default.join(process.cwd(), 'sample-md-files');
        if (!fs_1.default.existsSync(documentsDir)) {
            throw new Error(`Documents directory not found: ${documentsDir}`);
        }
        const files = fs_1.default.readdirSync(documentsDir).filter(file => file.endsWith('.md'));
        if (files.length === 0) {
            throw new Error('No markdown files found in documents directory');
        }
        console.log(`[DOCS] Found ${files.length} markdown files to process`);
        for (const file of files) {
            const filePath = path_1.default.join(documentsDir, file);
            const content = fs_1.default.readFileSync(filePath, 'utf-8');
            console.log(`[DOCS] Processing ${file}...`);
            await this.processDocument(content, file);
        }
    }
    async processDocument(content, source) {
        const chunks = this.chunkDocument(content, source);
        console.log(`[DOCS] Created ${chunks.length} chunks for ${source}`);
        const texts = chunks.map(chunk => chunk.content);
        const embeddings = await this.embeddingService.generateEmbeddings(texts);
        for (let i = 0; i < chunks.length; i++) {
            chunks[i].embedding = embeddings[i].embedding;
        }
        this.chunks.push(...chunks);
    }
    chunkDocument(content, source) {
        const chunkSize = parseInt(process.env.CHUNK_SIZE || '500');
        const chunkOverlap = parseInt(process.env.CHUNK_OVERLAP || '50');
        const cleanContent = content.replace(/\r\n/g, '\n').replace(/\r/g, '\n');
        const sentences = cleanContent.split(/[.!?]+/).filter(s => s.trim().length > 0);
        const chunks = [];
        let currentChunk = '';
        let currentLength = 0;
        for (const sentence of sentences) {
            const trimmedSentence = sentence.trim();
            if (trimmedSentence.length === 0)
                continue;
            const sentenceWithPunctuation = trimmedSentence + '.';
            if (currentLength + sentenceWithPunctuation.length > chunkSize && currentChunk.length > 0) {
                chunks.push({
                    id: (0, uuid_1.v4)(),
                    content: currentChunk.trim(),
                    source,
                    metadata: {
                        chunk_index: chunks.length,
                        character_count: currentChunk.length
                    }
                });
                const words = currentChunk.split(' ');
                const overlapWords = words.slice(-chunkOverlap);
                currentChunk = overlapWords.join(' ') + ' ' + sentenceWithPunctuation;
                currentLength = currentChunk.length;
            }
            else {
                currentChunk += (currentChunk.length > 0 ? ' ' : '') + sentenceWithPunctuation;
                currentLength = currentChunk.length;
            }
        }
        if (currentChunk.trim().length > 0) {
            chunks.push({
                id: (0, uuid_1.v4)(),
                content: currentChunk.trim(),
                source,
                metadata: {
                    chunk_index: chunks.length,
                    character_count: currentChunk.length
                }
            });
        }
        return chunks;
    }
    async searchRelevantChunks(query, maxChunks = 3) {
        if (!this.isInitialized) {
            await this.initialize();
        }
        if (this.chunks.length === 0) {
            return [];
        }
        const queryEmbedding = await this.embeddingService.generateEmbedding(query);
        const chunksWithSimilarity = this.chunks
            .filter(chunk => chunk.embedding && chunk.embedding.length > 0)
            .map(chunk => ({
            chunk,
            similarity: EmbeddingService_1.EmbeddingService.cosineSimilarity(queryEmbedding.embedding, chunk.embedding)
        }))
            .sort((a, b) => b.similarity - a.similarity)
            .slice(0, maxChunks);
        console.log(`[DOCS] Found ${chunksWithSimilarity.length} relevant chunks for query: "${query.substring(0, 50)}..."`);
        return chunksWithSimilarity.map(item => item.chunk);
    }
    getAvailableSources() {
        return [...new Set(this.chunks.map(chunk => chunk.source))];
    }
    getTotalChunks() {
        return this.chunks.length;
    }
}
exports.DocumentService = DocumentService;
//# sourceMappingURL=DocumentService.js.map