{"version": 3, "file": "LLMService.js", "sourceRoot": "", "sources": ["../../src/services/LLMService.ts"], "names": [], "mappings": ";;;;;;AAAA,oDAA4B;AAG5B,MAAa,UAAU;IAIrB;QACE,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,CAAC;YAChC,MAAM,IAAI,KAAK,CAAC,iDAAiD,CAAC,CAAC;QACrE,CAAC;QAED,IAAI,CAAC,MAAM,GAAG,IAAI,gBAAM,CAAC;YACvB,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,cAAc;SACnC,CAAC,CAAC;QAEH,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,IAAI,OAAO,CAAC;IACnD,CAAC;IAKD,KAAK,CAAC,gBAAgB,CAAC,OAA4B;QACjD,IAAI,CAAC;YACH,MAAM,YAAY,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;YAErD,OAAO,CAAC,GAAG,CAAC,0CAA0C,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;YACpE,OAAO,CAAC,GAAG,CAAC,+BAA+B,YAAY,CAAC,MAAM,aAAa,CAAC,CAAC;YAE7E,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;gBACzD,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,QAAQ,EAAE;oBACR;wBACE,IAAI,EAAE,QAAQ;wBACd,OAAO,EAAE,YAAY;qBACtB;oBACD;wBACE,IAAI,EAAE,MAAM;wBACZ,OAAO,EAAE,OAAO,CAAC,YAAY;qBAC9B;iBACF;gBACD,WAAW,EAAE,GAAG;gBAChB,UAAU,EAAE,IAAI;gBAChB,KAAK,EAAE,GAAG;gBACV,iBAAiB,EAAE,GAAG;gBACtB,gBAAgB,EAAE,GAAG;aACtB,CAAC,CAAC;YAEH,MAAM,gBAAgB,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,OAAO,CAAC;YAE/D,IAAI,CAAC,gBAAgB,EAAE,CAAC;gBACtB,MAAM,IAAI,KAAK,CAAC,mCAAmC,CAAC,CAAC;YACvD,CAAC;YAED,OAAO,CAAC,GAAG,CAAC,6BAA6B,gBAAgB,CAAC,MAAM,aAAa,CAAC,CAAC;YAC/E,OAAO,gBAAgB,CAAC;QAE1B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;YAEzD,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;gBAC3B,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC;oBACzC,MAAM,IAAI,KAAK,CAAC,oDAAoD,CAAC,CAAC;gBACxE,CAAC;qBAAM,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,oBAAoB,CAAC,EAAE,CAAC;oBACxD,MAAM,IAAI,KAAK,CAAC,uDAAuD,CAAC,CAAC;gBAC3E,CAAC;qBAAM,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,iBAAiB,CAAC,EAAE,CAAC;oBACrD,MAAM,IAAI,KAAK,CAAC,0DAA0D,CAAC,CAAC;gBAC9E,CAAC;YACH,CAAC;YAED,MAAM,IAAI,KAAK,CAAC,gCAAgC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,EAAE,CAAC,CAAC;QAC9G,CAAC;IACH,CAAC;IAKO,iBAAiB,CAAC,OAA4B;QACpD,IAAI,MAAM,GAAG;;;;;;;;;;;;;;CAchB,CAAC;QAGE,IAAI,OAAO,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACtC,MAAM,IAAI;CACf,CAAC;YACI,OAAO,CAAC,cAAc,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;gBACvC,MAAM,IAAI,KAAK,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE,OAAO,OAAO,CAAC,OAAO,IAAI,CAAC;YACtE,CAAC,CAAC,CAAC;YACH,MAAM,IAAI,IAAI,CAAC;QACjB,CAAC;aAAM,CAAC;YACN,MAAM,IAAI;;;CAGf,CAAC;QACE,CAAC;QAGD,IAAI,OAAO,CAAC,WAAW,IAAI,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACjE,MAAM,IAAI;;;CAGf,CAAC;YACI,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE;gBAClD,MAAM,IAAI,YAAY,KAAK,GAAG,CAAC,KAAK,KAAK,CAAC,MAAM;EACtD,KAAK,CAAC,OAAO;;CAEd,CAAC;YACI,CAAC,CAAC,CAAC;QACL,CAAC;QAGD,IAAI,OAAO,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACtC,MAAM,IAAI;;;CAGf,CAAC;YACI,OAAO,CAAC,cAAc,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;gBACtC,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;oBACnB,MAAM,IAAI,KAAK,MAAM,CAAC,WAAW,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC;CAC9E,CAAC;gBACM,CAAC;qBAAM,CAAC;oBACN,MAAM,IAAI,KAAK,MAAM,CAAC,WAAW,eAAe,MAAM,CAAC,KAAK;CACrE,CAAC;gBACM,CAAC;YACH,CAAC,CAAC,CAAC;YACH,MAAM,IAAI,IAAI,CAAC;QACjB,CAAC;QAED,MAAM,IAAI;GACX,OAAO,CAAC,YAAY;;yKAEkJ,CAAC;QAEtK,OAAO,MAAM,CAAC;IAChB,CAAC;IAKD,KAAK,CAAC,cAAc;QAClB,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;gBACzD,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,QAAQ,EAAE;oBACR;wBACE,IAAI,EAAE,MAAM;wBACZ,OAAO,EAAE,oDAAoD;qBAC9D;iBACF;gBACD,UAAU,EAAE,EAAE;aACf,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,OAAO,CAAC;YACtD,OAAO,OAAO,EAAE,QAAQ,CAAC,uBAAuB,CAAC,IAAI,OAAO,EAAE,QAAQ,CAAC,YAAY,CAAC,IAAI,KAAK,CAAC;QAChG,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;YACtD,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAKD,YAAY;QACV,OAAO;YACL,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,QAAQ,EAAE,QAAQ;SACnB,CAAC;IACJ,CAAC;CACF;AAnLD,gCAmLC"}