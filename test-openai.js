#!/usr/bin/env node

/**
 * Simple OpenAI API Test
 * Tests if the OpenAI API key is working
 */

require('dotenv').config();
const OpenAI = require('openai');

async function testOpenAI() {
  console.log('🔍 Testing OpenAI API connection...');
  
  if (!process.env.OPENAI_API_KEY) {
    console.log('❌ OPENAI_API_KEY not found in environment variables');
    return false;
  }
  
  console.log('✅ API key found:', process.env.OPENAI_API_KEY.substring(0, 20) + '...');
  
  const openai = new OpenAI({
    apiKey: process.env.OPENAI_API_KEY,
  });

  try {
    console.log('🧪 Testing chat completion...');
    const chatResponse = await openai.chat.completions.create({
      model: 'gpt-3.5-turbo',
      messages: [
        {
          role: 'user',
          content: 'Say "Hello, API test successful!"'
        }
      ],
      max_tokens: 20
    });

    console.log('✅ Chat completion successful:', chatResponse.choices[0]?.message?.content);

    console.log('🧪 Testing embedding...');
    const embeddingResponse = await openai.embeddings.create({
      model: 'text-embedding-ada-002',
      input: 'Hello world test',
    });

    console.log('✅ Embedding successful, vector length:', embeddingResponse.data[0]?.embedding?.length);
    
    return true;
  } catch (error) {
    console.log('❌ OpenAI API Error:', error.message);
    if (error.status) {
      console.log('   Status:', error.status);
    }
    if (error.code) {
      console.log('   Code:', error.code);
    }
    return false;
  }
}

testOpenAI().then(success => {
  if (success) {
    console.log('🎉 OpenAI API is working correctly!');
    process.exit(0);
  } else {
    console.log('💥 OpenAI API test failed');
    process.exit(1);
  }
}).catch(error => {
  console.error('💥 Test runner failed:', error.message);
  process.exit(1);
});
