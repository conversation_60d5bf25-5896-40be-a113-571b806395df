import { <PERSON>lug<PERSON>, <PERSON>lug<PERSON><PERSON><PERSON><PERSON>, MathResult } from '../types';
export declare class MathPlugin implements Plugin {
    name: string;
    description: string;
    shouldExecute(input: string): boolean;
    execute(input: string): Promise<PluginResult>;
    private extractMathExpression;
    private isValidExpression;
    private evaluateExpression;
    private handleMathFunctions;
    private safeEval;
    static formatMathResult(data: MathResult): string;
}
//# sourceMappingURL=MathPlugin.d.ts.map