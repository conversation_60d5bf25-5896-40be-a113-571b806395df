"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.agentRouter = void 0;
const express_1 = require("express");
const AgentService_1 = require("../services/AgentService");
const errorHandler_1 = require("../middleware/errorHandler");
const router = (0, express_1.Router)();
exports.agentRouter = router;
const agentService = new AgentService_1.AgentService();
const validateAgentMessage = (req, res, next) => {
    const { message, session_id } = req.body;
    if (!message || typeof message !== 'string' || message.trim().length === 0) {
        throw (0, errorHandler_1.createError)('Message is required and must be a non-empty string', 400);
    }
    if (!session_id || typeof session_id !== 'string' || session_id.trim().length === 0) {
        throw (0, errorHandler_1.createError)('Session ID is required and must be a non-empty string', 400);
    }
    if (message.length > 4000) {
        throw (0, errorHandler_1.createError)('Message is too long. Maximum length is 4000 characters', 400);
    }
    next();
};
router.post('/message', validateAgentMessage, async (req, res, next) => {
    try {
        const { message, session_id } = req.body;
        console.log(`[AGENT] Processing message for session: ${session_id}`);
        console.log(`[AGENT] Message: ${message.substring(0, 100)}${message.length > 100 ? '...' : ''}`);
        const response = await agentService.processMessage(message, session_id);
        res.json(response);
    }
    catch (error) {
        next(error);
    }
});
router.get('/sessions/:session_id/memory', async (req, res, next) => {
    try {
        const { session_id } = req.params;
        if (!session_id) {
            throw (0, errorHandler_1.createError)('Session ID is required', 400);
        }
        const memory = await agentService.getSessionMemory(session_id);
        res.json({
            session_id,
            memory,
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        next(error);
    }
});
router.delete('/sessions/:session_id', async (req, res, next) => {
    try {
        const { session_id } = req.params;
        if (!session_id) {
            throw (0, errorHandler_1.createError)('Session ID is required', 400);
        }
        await agentService.clearSessionMemory(session_id);
        res.json({
            message: 'Session memory cleared successfully',
            session_id,
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        next(error);
    }
});
router.get('/health', async (req, res, next) => {
    try {
        const healthStatus = await agentService.getHealthStatus();
        res.status(healthStatus.status === 'healthy' ? 200 :
            healthStatus.status === 'degraded' ? 206 : 503)
            .json(healthStatus);
    }
    catch (error) {
        next(error);
    }
});
router.get('/info', (req, res, next) => {
    try {
        const agentInfo = agentService.getAgentInfo();
        res.json(agentInfo);
    }
    catch (error) {
        next(error);
    }
});
//# sourceMappingURL=agent.js.map