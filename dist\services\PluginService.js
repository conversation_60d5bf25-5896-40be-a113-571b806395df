"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PluginService = void 0;
const WeatherPlugin_1 = require("../plugins/WeatherPlugin");
const MathPlugin_1 = require("../plugins/MathPlugin");
class PluginService {
    constructor() {
        this.plugins = [];
        this.initializePlugins();
    }
    initializePlugins() {
        this.plugins = [
            new WeatherPlugin_1.WeatherPlugin(),
            new MathPlugin_1.MathPlugin()
        ];
        console.log(`[PLUGINS] Initialized ${this.plugins.length} plugins: ${this.plugins.map(p => p.name).join(', ')}`);
    }
    async executePlugins(input) {
        const results = [];
        console.log(`[PLUGINS] Checking plugins for input: "${input.substring(0, 100)}..."`);
        const pluginsToExecute = this.plugins.filter(plugin => {
            const shouldExecute = plugin.shouldExecute(input);
            if (shouldExecute) {
                console.log(`[PLUGINS] Plugin "${plugin.name}" will execute`);
            }
            return shouldExecute;
        });
        if (pluginsToExecute.length === 0) {
            console.log('[PLUGINS] No plugins triggered for this input');
            return results;
        }
        const pluginPromises = pluginsToExecute.map(async (plugin) => {
            try {
                const startTime = Date.now();
                const result = await plugin.execute(input);
                const duration = Date.now() - startTime;
                console.log(`[PLUGINS] Plugin "${plugin.name}" executed in ${duration}ms - Success: ${result.success}`);
                return result;
            }
            catch (error) {
                console.error(`[PLUGINS] Plugin "${plugin.name}" failed:`, error);
                return {
                    plugin_name: plugin.name,
                    result: null,
                    success: false,
                    error: error instanceof Error ? error.message : 'Unknown plugin error'
                };
            }
        });
        try {
            const pluginResults = await Promise.all(pluginPromises);
            results.push(...pluginResults);
        }
        catch (error) {
            console.error('[PLUGINS] Error executing plugins:', error);
        }
        return results;
    }
    getAvailablePlugins() {
        return this.plugins.map(plugin => ({
            name: plugin.name,
            description: plugin.description
        }));
    }
    wouldExecutePlugins(input) {
        return this.plugins.some(plugin => plugin.shouldExecute(input));
    }
    getPlugin(name) {
        return this.plugins.find(plugin => plugin.name === name);
    }
    formatPluginResults(results) {
        if (results.length === 0) {
            return '';
        }
        let formatted = '\n\n## Plugin Results\n\n';
        results.forEach(result => {
            if (result.success) {
                switch (result.plugin_name) {
                    case 'weather':
                        formatted += WeatherPlugin_1.WeatherPlugin.formatWeatherData(result.result) + '\n\n';
                        break;
                    case 'math':
                        formatted += MathPlugin_1.MathPlugin.formatMathResult(result.result) + '\n\n';
                        break;
                    default:
                        formatted += `**${result.plugin_name}**: ${JSON.stringify(result.result)}\n\n`;
                }
            }
            else {
                formatted += `**${result.plugin_name}**: ❌ ${result.error}\n\n`;
            }
        });
        return formatted;
    }
    getPluginStats() {
        return {
            total_plugins: this.plugins.length,
            plugin_names: this.plugins.map(p => p.name),
            plugins_info: this.getAvailablePlugins()
        };
    }
    async testAllPlugins() {
        const testInputs = {
            weather: 'What is the weather in London?',
            math: 'Calculate 2 + 2 * 5'
        };
        const results = {};
        for (const plugin of this.plugins) {
            const testInput = testInputs[plugin.name];
            if (testInput) {
                try {
                    const shouldExecute = plugin.shouldExecute(testInput);
                    if (shouldExecute) {
                        const result = await plugin.execute(testInput);
                        results[plugin.name] = result.success;
                    }
                    else {
                        results[plugin.name] = false;
                    }
                }
                catch (error) {
                    console.error(`[PLUGINS] Test failed for ${plugin.name}:`, error);
                    results[plugin.name] = false;
                }
            }
            else {
                results[plugin.name] = false;
            }
        }
        return results;
    }
}
exports.PluginService = PluginService;
//# sourceMappingURL=PluginService.js.map